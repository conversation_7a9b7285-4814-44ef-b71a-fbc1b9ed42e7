// pending, fulfilled (Resolved), rejected
let promise = new Promise((resolve, reject) => {
  if (20 > 10) {
    resolve("This condition is correct");
  } else {
    reject("This condition is incorrect");
  }
});

// // then is used to handle the resolved state
// promise
//   .then((resolve) => {
//     console.log(resolve);
//   })
//   .catch((reject) => {
//     console.log(reject);
//   }); //catch is used to handle the rejected state

// GET, POST, PUT, DELETE - CRUD
// GET is used to fetch data from the server
// POST is used to send data to the server
// PUT is used to update data on the server
// DELETE is used to delete data from the server

// fetch("https://jsonplaceholder.typicode.com/users")
//   .then((response) => {
//     console.log(response);
//     return response.json();
//   })
//   .then((data) => {
//     console.log(data);
//   });

// Promise.all, Promise.allSettled, Promise.race, Promise.any, Promise.resolve, Promise.reject

// all is used to wait for all promises to be resolved, if any one promise is rejected then the whole promise is rejected
// allSettled is used to wait for all promises to be resolved or rejected
// race is used to wait for the first promise to be resolved or rejected
// any is used to wait for the first promise to be resolved
// resolve is used to resolve a promise
// reject is used to reject a promise

//  there is a solution for multiple .then() calls
