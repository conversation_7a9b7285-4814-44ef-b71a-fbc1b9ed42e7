// String Methods

// length, replace, concat, toUpperCase, toLowerCase, indexOf, lastIndexOf, charAt, at, split, substring, slice, trim, trimStart, trimEnd, padStart, padEnd, replaceAll, includes

let str = "Hello World";

console.log("length: ", str.length);

console.log("replace:", str.replace("l", "t")); // replace the first occurrence of the string
console.log("replaceAll:", str.replaceAll("l", "t")); // replace all the occurrences of the string

console.log("concat:", str.concat(" John"));

console.log("toUpperCase:", str.toUpperCase());
console.log("toLowerCase:", str.toLowerCase());

console.log("indexOf:", str.indexOf("l")); // first occurrence of the string
console.log("indexOf:", str.indexOf("z")); // -1 if the string is not found

console.log("lastIndexOf:", str.lastIndexOf("o")); // last occurrence of the string
console.log("lastIndexOf:", str.lastIndexOf("z")); // -1 if the string is not found

console.log("charAt:", str.charAt(2)); // accepts only positive index
console.log("at:", str.at(-1)); // accepts both positive and negative index

console.log("split:", str.split(" "));

console.log("substring:", str.substring(2, 7)); // n - 1
console.log("slice:", str.slice(2, 7)); // n - 1
console.log("slice:", str.slice(6));
console.log("slice:", str.slice(6, 11));

let str2 = "   Hello      World   ";
console.log("trim:", str2.trim()); // remove the space from the start and end of the string
console.log("trimStart:", str2.trimStart()); // remove the space from the start of the string
console.log("trimEnd:", str2.trimEnd()); // remove the space from the end of the string

let str3 = "Hello";
console.log("padStart:", str3.padStart(20, "&")); // add the string to the start of the string
console.log("padEnd:", str3.padEnd(20, "&")); // add the string to the end of the string

console.log("includes:", str.includes("l")); // true if the string is found
console.log("includes:", str.includes("z")); // false if the string is not found
