<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>JSONPlaceholder Users Demo</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          animation: {
            'fade-in': 'fadeIn 0.5s ease-in-out',
            'slide-up': 'slideUp 0.6s ease-out'
          }
        }
      }
    }
  </script>
  <style>
    @keyframes fadeIn {
      from {
        opacity: 0;
      }

      to {
        opacity: 1;
      }
    }

    @keyframes slideUp {
      from {
        transform: translateY(20px);
        opacity: 0;
      }

      to {
        transform: translateY(0);
        opacity: 1;
      }
    }
  </style>
</head>

<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
  <!-- Header -->
  <header class="bg-white shadow-lg">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="text-center">
        <h1 class="text-4xl font-bold text-gray-900 mb-2">JSONPlaceholder Users</h1>
        <p class="text-lg text-gray-600">Demo of https://jsonplaceholder.typicode.com/users API</p>
        <div class="mt-4 flex justify-center items-center space-x-4">
          <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
            <span class="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
            Live Data
          </span>
          <button id="refreshBtn"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
              </path>
            </svg>
            Refresh Data
          </button>
        </div>
      </div>
    </div>
  </header>

  <!-- Loading State -->
  <div id="loading" class="flex justify-center items-center py-12">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
    <span class="ml-3 text-lg text-gray-600">Loading users...</span>
  </div>

  <!-- Error State -->
  <div id="error" class="hidden max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="bg-red-50 border border-red-200 rounded-md p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
              clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Error loading users</h3>
          <p class="mt-1 text-sm text-red-700" id="errorMessage"></p>
        </div>
      </div>
    </div>
  </div>

  <!-- Users Grid -->
  <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div id="usersGrid" class="hidden grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- User cards will be inserted here -->
    </div>
  </main>

  <script>
    const API_URL = 'https://jsonplaceholder.typicode.com/users';

    function fetchUsers() {
      const loading = document.getElementById('loading');
      const error = document.getElementById('error');
      const usersGrid = document.getElementById('usersGrid');

      // Show loading state
      loading.classList.remove('hidden');
      error.classList.add('hidden');
      usersGrid.classList.add('hidden');

      return fetch(API_URL)
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          return response.json();
        })
        .then(users => {
          displayUsers(users);
        })
        .catch(err => {
          showError(err.message);
        })
        .finally(() => {
          loading.classList.add('hidden');
        });
    }

    function showError(message) {
      const error = document.getElementById('error');
      const errorMessage = document.getElementById('errorMessage');
      errorMessage.textContent = message;
      error.classList.remove('hidden');
    }

    function displayUsers(users) {
      const usersGrid = document.getElementById('usersGrid');
      usersGrid.innerHTML = '';

      users.forEach((user, index) => {
        usersGrid.appendChild(createUserCard(user, index));
      });

      usersGrid.classList.remove('hidden');
    }

    function createUserCard(user, index) {
      const card = document.createElement('div');
      card.className = 'bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden animate-slide-up';
      card.style.animationDelay = `${index * 0.1}s`;

      card.innerHTML = `
                <div class="p-6">
                    <!-- User Header -->
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                            ${user.name.charAt(0)}
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-gray-900">${user.name}</h3>
                            <p class="text-sm text-gray-500">@${user.username}</p>
                        </div>
                    </div>
                    
                    <!-- Contact Info -->
                    <div class="space-y-3 mb-4">
                        <div class="flex items-center text-sm text-gray-600">
                            <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            <a href="mailto:${user.email}" class="hover:text-indigo-600 transition-colors">${user.email}</a>
                        </div>
                        
                        <div class="flex items-center text-sm text-gray-600">
                            <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                            <span>${user.phone}</span>
                        </div>
                        
                        <div class="flex items-center text-sm text-gray-600">
                            <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                            </svg>
                            <a href="http://${user.website}" target="_blank" class="hover:text-indigo-600 transition-colors">${user.website}</a>
                        </div>
                        
                        <div class="flex items-start text-sm text-gray-600">
                            <svg class="w-4 h-4 mr-2 mt-0.5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <div>
                                <div>${user.address.street}, ${user.address.suite}</div>
                                <div>${user.address.city}, ${user.address.zipcode}</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Company Info -->
                    <div class="border-t pt-4">
                        <h4 class="font-medium text-gray-900 mb-2">${user.company.name}</h4>
                        <p class="text-sm text-gray-600 italic">"${user.company.catchPhrase}"</p>
                        <p class="text-xs text-gray-500 mt-1">${user.company.bs}</p>
                    </div>
                </div>
            `;

      return card;
    }

    // Event listeners
    document.getElementById('refreshBtn').addEventListener('click', fetchUsers);

    // Initial load
    fetchUsers();
  </script>
</body>

</html>