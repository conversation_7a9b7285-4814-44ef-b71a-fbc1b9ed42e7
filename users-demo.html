<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>JSONPlaceholder Users Demo</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    @keyframes slideUp {
      from {
        transform: translateY(20px);
        opacity: 0;
      }

      to {
        transform: translateY(0);
        opacity: 1;
      }
    }

    .slide-up {
      animation: slideUp 0.6s ease-out;
    }
  </style>
</head>

<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
  <header class="bg-white shadow-lg text-center py-6">
    <h1 class="text-3xl font-bold text-gray-900">JSONPlaceholder Users</h1>
    <p class="text-gray-600 mt-2">Demo of https://jsonplaceholder.typicode.com/users API</p>
    <button id="refreshBtn" class="mt-4 px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700">
      🔄 Refresh Data
    </button>
  </header>

  <main class="max-w-6xl mx-auto p-4">
    <div id="loading" class="text-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
      <p class="mt-2 text-gray-600">Loading...</p>
    </div>
    <div id="error" class="hidden bg-red-100 border border-red-300 text-red-700 px-4 py-3 rounded mb-4">
      <span id="errorMessage"></span>
    </div>
    <div id="usersGrid" class="hidden grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"></div>
  </main>

  <script>
    const $ = id => document.getElementById(id);
    const API_URL = 'https://jsonplaceholder.typicode.com/users';

    function fetchUsers() {
      $('loading').classList.remove('hidden');
      $('error').classList.add('hidden');
      $('usersGrid').classList.add('hidden');

      fetch(API_URL)
        .then(res => res.ok ? res.json() : Promise.reject(`Error: ${res.status}`))
        .then(users => {
          $('usersGrid').innerHTML = users.map((user, i) => createUserCard(user, i)).join('');
          $('usersGrid').classList.remove('hidden');
        })
        .catch(err => {
          $('errorMessage').textContent = err;
          $('error').classList.remove('hidden');
        })
        .finally(() => $('loading').classList.add('hidden'));
    }

    function createUserCard(user, i) {
      return `
        <div class="bg-white rounded-lg shadow hover:shadow-lg transition-shadow p-4 slide-up" style="animation-delay:${i * 0.1}s">
          <div class="flex items-center mb-3">
            <div class="w-10 h-10 bg-indigo-500 rounded-full flex items-center justify-center text-white font-bold">
              ${user.name[0]}
            </div>
            <div class="ml-3">
              <h3 class="font-semibold">${user.name}</h3>
              <p class="text-sm text-gray-500">@${user.username}</p>
            </div>
          </div>
          <div class="space-y-2 text-sm">
            <div>📧 <a href="mailto:${user.email}" class="text-blue-600">${user.email}</a></div>
            <div>📞 ${user.phone}</div>
            <div>🌐 <a href="http://${user.website}" target="_blank" class="text-blue-600">${user.website}</a></div>
            <div>📍 ${user.address.city}, ${user.address.zipcode}</div>
            <div class="border-t pt-2 mt-2">
              <div class="font-medium">${user.company.name}</div>
              <div class="text-xs text-gray-600">"${user.company.catchPhrase}"</div>
            </div>
          </div>
        </div>`;
    }

    $('refreshBtn').onclick = fetchUsers;
    fetchUsers();
  </script>
</body>

</html>