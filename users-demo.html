<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Users Demo</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="bg-gray-100 p-4">
  <div class="max-w-6xl mx-auto">
    <h1 class="text-2xl font-bold text-center mb-4">JSONPlaceholder Users</h1>
    <div class="text-center mb-4">
      <button id="btn" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">🔄 Refresh</button>
    </div>
    <div id="loading" class="text-center">Loading...</div>
    <div id="error" class="hidden text-red-600 text-center mb-4"></div>
    <div id="grid" class="hidden grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"></div>
  </div>

  <script>
    const $ = id => document.getElementById(id);

    function load() {
      $('loading').hidden = false;
      $('error').hidden = true;
      $('grid').hidden = true;

      fetch('https://jsonplaceholder.typicode.com/users')
        .then(r => r.json())
        .then(users => {
          $('grid').innerHTML = users.map(u => `
            <div class="bg-white rounded shadow p-4">
              <div class="flex items-center mb-2">
                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                  ${u.name[0]}
                </div>
                <div class="ml-2">
                  <h3 class="font-semibold text-sm">${u.name}</h3>
                  <p class="text-xs text-gray-500">@${u.username}</p>
                </div>
              </div>
              <div class="text-xs space-y-1">
                <div>📧 ${u.email}</div>
                <div>📞 ${u.phone}</div>
                <div>🌐 ${u.website}</div>
                <div>📍 ${u.address.city}</div>
                <div class="font-medium">${u.company.name}</div>
              </div>
            </div>`).join('');
          $('grid').hidden = false;
        })
        .catch(e => {
          $('error').textContent = 'Error: ' + e.message;
          $('error').hidden = false;
        })
        .finally(() => $('loading').hidden = true);
    }

    $('btn').onclick = load;
    load();
  </script>
</body>

</html>
</script>
</body>

</html>