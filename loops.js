let sweetArray = ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Mysore Pak"];

for (let i = 0; i < sweetArray.length; i++) {
  // console.log(sweetArray[i]);
}

// for of loop
for (let sweet of sweetArray) {
  // console.log(sweet);
}

// for in loop returns the index of the array
for (let index in sweetArray) {
  // console.log(index);
  // console.log(sweetArray[index]);
}

let items = {
  id: 1,
  itemName: "pen",
  price: 2234,
};

// for (let item of items) {
//   // can't apply to single object
//   console.log(item);
// }

for (let item in items) {
  console.log(item); // key
  console.log(items[item]); // value
}

// let itemsArray = [
//   {
//     id: 1,
//     itemName: "pen",
//     price: 2234,
//   },
//   {
//     id: 2,
//     itemName: "pencil",
//     price: 10,
//   },
// ];

// for (let item of itemsArray) {
//   console.log(item);
// }
