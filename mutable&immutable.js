let a = 30;
let b = a;
a = 40;
// console.log("a:", a);
// console.log("b:", b);

let person = {
  name: "<PERSON>",
  age: 20,
};

// let person2 = person;
// person2.age = 30;
// console.log("person:", person);
// console.log("person2:", person2);

// localStorage.setItem("name", "<PERSON>");
// console.log(localStorage.getItem("name"));

// localStorage.setItem("personObj", JSON.stringify(person));

// let person3 = localStorage.getItem("personObj");
// console.log(JSON.parse(person3));

// localStorage.removeItem("name");

// localStorage.clear();

sessionStorage.setItem("bird", "kiwi");
sessionStorage.setItem("animal", "dog");
sessionStorage.getItem("bird");
