const person = {
  fName: "<PERSON>",
  lName: "<PERSON>",
  age: 22,
  address: {
    city: "Bengaluru",
    pincode: 123456,
  },
  hobbies: ["reading", "writing", "dancing", "games", "gardening"],
};

// let personCopy = person; // copy
// spread operator
let personCopy = { ...person }; // shallow copy
let personCopy1 = { ...person, address: { ...person.address } };

person.fName = "Mohit";
person.address.city = "Mysore";
// console.log("person:", person);
// console.log("personCopy:", personCopy);
// console.log("personCopy1:", personCopy1);
// let string = JSON.stringify(person);
// let jsonObj = JSON.parse(string); // deep copy
// jsonObj.address.pincode = 454343;
// console.log("person:", person);
// console.log("jsonObj:", jsonObj);

function add(...nums) {
  return nums.reduce((acc, curr) => acc + curr);
}
console.log("add:", add(10, 20, 30, 40, 50, 60, 70, 80, 90, 100));

function greet(name = "Guest") {
  // default parameter
  console.log(`Hello ${name}! Welcome`);
}
greet("Harry");
