function greet(sample) {
  let fname = "<PERSON>";
  console.log(`Hello ${fname}, Welcome`);
  sample(fname);
  console.log("sdfghj");
}

function welcome(name) {
  console.log(name, "Welcome to callback functions class");
}

// greet();
setTimeout(() => {
  greet(welcome);
}, 2000); // This waits for 2000 milliseconds (2 seconds) before calling the greet function.
// welcome("Mary");

// callback pyramid of doom (also known as "callback hell")

// map(() => {});

let intervalId = setInterval(() => {
  console.log("Hello");
}, 1000); // This prints "Hello" to the console every 1000 milliseconds (1 second).

// Stop after 5 seconds
setTimeout(() => {
  clearInterval(intervalId);
  console.log("Stopped");
}, 5000);
