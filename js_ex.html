<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
  <style>
    body {
      background-color: black;
    }
  </style>
</head>

<body>

  <!-- <script src="funcationss.js" type="text/javascript"></script> -->
  <!-- <script src="object.js" type="text/javascript"></script> -->
  <!-- <script src="stringMethods.js" type="text/javascript"></script> -->
  <!-- <script src="arrayMethods.js" type="text/javascript"></script> -->
  <!-- <script src="callback.js" type="text/javascript"></script> -->
  <!-- <script src="objectMethods.js" type="text/javascript"></script> -->
  <!-- <script src="spread&rest.js" type="text/javascript"></script> -->
  <!-- <script src="loops.js" type="text/javascript"></script> -->
  <!-- <script src="mutable&immutable.js" type="text/javascript"></script> -->
  <script src="promises.js" type="text/javascript"></script>
</body>

</html>