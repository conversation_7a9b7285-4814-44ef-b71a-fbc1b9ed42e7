let person = {
  fname: "<PERSON>",
  lname: "<PERSON><PERSON>",
  age: 20,
  address: {
    city: "New York",
    state: "NY",
    country: "USA",
  },
};
console.log("person", person);

console.log("First Name:", person.fname);
console.log("Last Name:", person.lname);
console.log("Age:", person.age);
console.log("Address:");
console.log(" City:", person.address.city);
console.log(" State:", person.address.state);
console.log(" Country:", person.address.country);

// destructuring - extracting the properties of the objects/arrays

let {
  fname,
  lname,
  age,
  address: { city, state, country },
} = person;

console.log("fname", fname);
console.log("lname", lname);
console.log("age", age);
console.log("city", city);
console.log("state", state);
console.log("country", country);
