// named function
// anonymous function
// IIFE (Immediately Invoked Function Expression) function
// arrow function

// named function
function add(a, b) {
  return a + b;
}
console.log("Named function", add(1, 2));

// anonymous function
const sum = function (a, b) {
  return a + b;
};
console.log("Anonymous function", sum(1, 2));

// IIFE (Immediately Invoked Function Expression) function
(function sub(a, b) {
  console.log("IIFE function", a - b);
})(1, 2);

// arrow function
const mul = (a, b) => {
  return a * b;
};
console.log("Arrow function", mul(1, 2));

// arrow function
const div = (a, b) => a / b;
console.log("Arrow function", div(1, 2));

// arrow function
const divs = (fname) => fname;
console.log("Arrow function", divs("John"));

// arrow function
// const divss = fname => fname;
// console.log("Arrow function", divss("John"));

// variable declaration ES6 - ECMAScript 6 - 2015
// var, let, const

// var a = 10; // in case of var we can declare the variable and assign the value to the variable
// a = 20;
// var a = 30; // in case of var we can redeclare the variable
// console.log("var", a);

// let b = 10; // in case of let we can assign the value to the variable
// b = 20;
// // let b = 30; // in case of let we can not redeclare the variable
// console.log("let", b);

// const c = 20; // in case of const we can not redeclare and reassign the value to the variable
// // c = 30;
// // const c = 40;
// console.log("const", c);

console.log("var", a);
var a = 10; // undefined

console.log("let", b);
let b = 10; // ReferenceError: Cannot access 'b' before initialization

// console.log("const", c);
// const c = 10;

// TDZ = Temporal Dead Zone only in case of let and const when we access the variable before the declaration

// when to use let and const

// when we think the variable changes we need to make variable as let

// when we think the variable does not change we need to make variable as const and nobody can declare the variable again

// var is function scoped
// let and const are block scoped

// var is hoisted
// let and const are not hoisted

// var is mutable
// let and const are immutable

// var is not a good practice to use
