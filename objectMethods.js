const person = {
  fName: "<PERSON>",
  lName: "<PERSON>",
  age: 22,
  address: {
    city: "Bengaluru",
    pincode: 123456,
  },
  hobbies: ["reading", "writing", "dancing", "games", "gardening"],
};

// console.log("Object.keys(person):", Object.keys(person));
// console.log("Object.values(person):", Object.values(person));
// console.log("Object.entries(person):", Object.entries(person));

// console.log("Object.freeze(person):", Object.freeze(person));
// person.fName = "Mohit";
// person.eyeColor = "brown";
// person.address.city = "Mumbai";
// console.log("person:", person);
// console.log("Object.isFrozen(person):", Object.isFrozen(person));

console.log("Object.seal(person):", Object.seal(person));
person.fName = "Mohit";
person.eyeColor = "brown";
person.address.city = "Mumbai";
console.log("person:", person);
console.log("Object.isSealed(person):", Object.isSealed(person));
