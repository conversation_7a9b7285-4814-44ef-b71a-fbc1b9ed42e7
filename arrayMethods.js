// length, pop, push, shift, unshift, slice, splice, toSpliced,
// find, findIndex, filter, sort, map, foreach, join,
// copyWithin, some, every, toString, flat, reduce, reduceRight
// indexOf, lastIndexOf, reverse, includes, fill

let arr = [10, 20, "Hello", "Class", 30, 40];

// console.log("length:", arr.length);

// console.log("unshift:", arr.unshift("sdfdf")); // add the element to the start of the array
// console.log("shift:", arr.shift()); // remove the element from the start of the array

// console.log("push:", arr.push("50")); // add the element to the end of the array
// console.log("arr:", arr);
// console.log("pop:", arr.pop()); // remove the element from the end of the array

// console.log("slice:", arr.slice(2, 4)); // n - 1
// console.log("slice:", arr.slice(2, 3));

// console.log("splice:", arr.splice(0, 2)); // n - 1
console.log("splice:", arr.splice(3, 1)); // delete the element from the array
console.log("arr:", arr);
console.log("toSpliced:", arr.toSpliced(3, 1, "sdfdf")); // add the element to the array

let arr1 = ["My", "name", "is", "karna"];
console.log("join:", arr1.join("."));

console.log("reverse:", arr1.reverse());

let str = "my name is raju";
console.log("split:", str.split(" ").reverse().join(" ")); // raju is name my
console.log("reverse chars:", str.split("").reverse().join("")); // yajur si eman ym

console.log("toString:", arr1.toString()); // default separator is comma

let arr2 = [10, 20, [30, 40, [50], 89], [[26, 89], 78]];
console.log("flat:", arr2.flat(2)); // two levels of depth
console.log("flat:", arr2.flat(Infinity)); // all levels of depth

// let arr3 = [10, 20, 3, 2, 4, 5];
// console.log(
//   "reduce:",
//   arr3.reduce((a, c) => a + c, 0)
// );

let arr3 = [10, 20, 30, 40, 50];
let array1 = [];
let result = arr3.forEach((item, ind) => {
  // console.log(item + 2);
  // return item + 2;
  console.log(ind);
  array1.push(item + 2);
});

// console.log("result:", result); // result is undefined
console.log("array1:", array1);

let result1 = arr3.map((item) => {
  return item + 2;
});

console.log("result 1:", result1);

// let res1 = arr3.find((e) => {
//   return e == 10;
// });

let res1 = arr3.find((e) => e > 30); // find the first element that satisfies the condition

console.log("res1:", res1);

console.log(arr3.findIndex((e) => e > 20)); // find the index of the first element that satisfies the condition

console.log(arr3.filter((e) => e > 20)); // find the elements that satisfies the condition

let arr4 = [7, 8, 3, 4, 1, 2, 9];

// console.log("arr4.sort():", arr4.sort()); // sort the array

// console.log(
//   "arr4.sort((a, b) => b - a):",
//   arr4.sort((a, b) => b - a)
// ); // descending sort the array

console.log(arr4.reduce((accValue, currentValue) => accValue + currentValue)); // left to right

console.log(arr4.reduceRight((accValue, currentValue) => accValue + currentValue)); // right to left

console.log(arr3.some((e) => e > 20)); // check if any element satisfies the condition

console.log(arr3.every((e) => e > 20)); // check if all elements satisfies the condition

// console.log(arr4.copyWithin(4, 1, 3)); // [7, 8, 3, 4, 8, 3, 9] // (target, start, end)

console.log(arr4.fill(10, 3, 5)); // [7, 8, 3, 10, 10, 2, 9] // (value, start, end)
