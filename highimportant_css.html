<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" href="highimportant_css.css"> <!-- external CSS -->
  <title>Document</title>
  <style>
    /* p {
      color: blue;
    } */

    .box {
      display: inline-block;
      width: 150px;
      height: 100px;
      background-color: coral;
      margin: 10px;
      justify-content: center;
      align-items: center;
    }
  </style>
</head>

<body>
  <div class="box">Box 1</div>
  <div class="box">Box 2</div>
  <span class="box">Box 3</span>
  <!-- <table border="1">
    <tr>
      <th rowspan="2">Name</th>
      <th colspan="2">Marks</th>
    </tr>
    <tr>
      <th>Math</th>
      <th>English</th>
    </tr>
    <tr>
      <td>John</td>
      <td>90</td>
      <td>85</td>
    </tr> -->
  <!-- <p style="color: red;">This text will be red.</p> inline CSS -->
  </table>

</body>

</html>