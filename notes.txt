JavaScript Engines
These are programs that parse, interpret, or compile JavaScript code, often used inside web browsers or standalone environments (like Node.js).

1. V8
Developed by: Google
Used in: Chrome, Node.js, Deno
Highlights: Compiles JS to machine code for high performance. Built with C++. Powers both front-end (Chrome) and back-end (Node.js) applications.

2. Chakra
Developed by: Microsoft
Used in: Microsoft Edge (legacy version, pre-Chromium)
Highlights: Now mostly deprecated since Edge switched to Chromium (and V8). ChakraCore was the open-source part of the engine.

3. SpiderMonkey
Developed by: Mozilla
Used in: Firefox
Highlights: First-ever JavaScript engine (created by <PERSON> in 1995). Supports Just-In-Time (JIT) compilation.

4. JavaScriptCore (JSC)
Developed by: Apple
Used in: Safari, WebKit-based browsers
Also known as: Nitro
Highlights: Focuses on energy efficiency and performance for macOS and iOS.

